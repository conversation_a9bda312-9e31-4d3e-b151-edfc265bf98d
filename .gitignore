# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules

# testing
coverage

# production
dist

# misc
.DS_Store

# local env files
.env.local
.env.development.local
.env.test.local
.env.production.local

# lock files
yarn.lock
package-lock.json

# debug files
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# extension.js
extension-env.d.ts
public/images/flags/*

chrome-debug-profile