const path = require("path");
const CopyPlugin = require("copy-webpack-plugin"); // Import the plugin

// todo: remove map files from the built package
module.exports = {
  mode: "development",
  devtool: "inline-source-map",
  ignoreWarnings: [
    {
      module: /node_modules/,
      message: /source map/,
    },
  ],
  entry: {
    content: "./content/content_script.ts",
    background: "./background.ts",
  },
  output: {
    path: path.resolve(__dirname, "dist/chrome"),
    filename: "[name].js",
  },

  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: [
          {
            loader: "ts-loader",
            options: {
              compilerOptions: {
                sourceMap: true,
              },
              transpileOnly: false,
            },
          },
        ],
        exclude: /node_modules/,
      },
      {
        test: /\.css$/,
        use: ["style-loader", "css-loader"],
      },
    ],
  },
  resolve: {
    extensions: [".tsx", ".ts", ".js"],
  },
  plugins: [
    new CopyPlugin({
      patterns: [
        { from: "images", to: "images" }, // Copy images folder from source to dist/chrome/images
      ],
    }),
  ],
};
