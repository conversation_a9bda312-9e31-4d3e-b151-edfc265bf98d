// Quick test script to diagnose Activate button issues on eBay
// Run this in the browser console on an eBay search results page

console.log('🔍 Testing uBuyAssist Activate Button Issues...');

// 1. Check if extension containers are detected
const newLayoutItems = document.querySelectorAll('li.su-card-container');
const oldLayoutItems = document.querySelectorAll('li.s-item');

console.log(`Found ${newLayoutItems.length} new layout items (li.su-card-container)`);
console.log(`Found ${oldLayoutItems.length} old layout items (li.s-item)`);

if (newLayoutItems.length === 0 && oldLayoutItems.length === 0) {
  console.log('❌ No search result containers found - this might not be a search results page');
} else {
  console.log('✅ Search result containers found');
}

// 2. Test seller detection on new layout
if (newLayoutItems.length > 0) {
  console.log('\n🔍 Testing seller detection on new layout...');
  
  const testItem = newLayoutItems[0];
  console.log('Testing first item:', testItem.id);
  
  // Look for seller info using current extension logic
  const secondaryContainers = testItem.querySelectorAll('.su-card-container__attributes__secondary');
  console.log(`Found ${secondaryContainers.length} secondary attribute containers`);
  
  let sellerFound = false;
  
  for (const container of secondaryContainers) {
    const attributeRows = container.querySelectorAll('.s-card__attribute-row');
    console.log(`  Found ${attributeRows.length} attribute rows`);
    
    for (const row of attributeRows) {
      const sellerElement = row.querySelector('.su-styled-text.secondary.large');
      if (sellerElement) {
        const text = sellerElement.textContent.trim();
        console.log(`  Checking text: "${text}"`);
        
        // Test the regex pattern used by the extension
        const match = text.match(/^(.+?) \(\d+[,\.\d]*\) \d+(\.\d+)?%/);
        if (match) {
          const sellerName = match[1].trim();
          console.log(`  ✅ SELLER FOUND: "${sellerName}"`);
          console.log(`  Element:`, sellerElement);
          console.log(`  Parent:`, sellerElement.parentElement);
          sellerFound = true;
          
          // 3. Test activate button injection
          console.log('\n🔍 Testing activate button injection...');
          
          // Check if button already exists
          const existingButton = testItem.querySelector('.ubuy-activate-container');
          if (existingButton) {
            console.log('  ✅ Activate button already exists:', existingButton);
          } else {
            console.log('  ❌ No activate button found');
            
            // Test where the button would be injected
            if (sellerElement.parentElement) {
              console.log('  Button would be injected after:', sellerElement.parentElement);
              console.log('  Parent element classes:', sellerElement.parentElement.className);
              
              // Create a test button to see if injection would work
              const testButton = document.createElement('span');
              testButton.className = 'ubuy-activate-container-test';
              testButton.style.cssText = 'background: red; color: white; padding: 4px 8px; margin: 4px; display: inline-block; border-radius: 3px; font-size: 12px;';
              testButton.textContent = 'TEST ACTIVATE';
              
              // Temporarily inject the test button
              sellerElement.parentElement.after(testButton);
              console.log('  ✅ Test button injected successfully');
              
              // Remove test button after 3 seconds
              setTimeout(() => {
                testButton.remove();
                console.log('  Test button removed');
              }, 3000);
            } else {
              console.log('  ❌ No parent element found for injection');
            }
          }
          break;
        } else {
          console.log(`  ❌ Text does not match seller pattern`);
        }
      }
    }
    if (sellerFound) break;
  }
  
  if (!sellerFound) {
    console.log('❌ No seller information found in the first item');
    
    // Try alternative selectors
    console.log('\n🔍 Trying alternative selectors...');
    
    // Look for any text that might be seller info
    const allSpans = testItem.querySelectorAll('span');
    for (const span of allSpans) {
      const text = span.textContent.trim();
      if (text.match(/\w+\s*\(\d+\)\s*\d+%/)) {
        console.log(`  Found potential seller text: "${text}"`);
        console.log(`  Element classes: "${span.className}"`);
        console.log(`  Parent classes: "${span.parentElement?.className}"`);
      }
    }
  }
}

// 4. Check if extension is loaded
console.log('\n🔍 Checking if extension is loaded...');
const extensionElements = document.querySelectorAll('.ubuyassist-detected-search-result');
console.log(`Found ${extensionElements.length} elements marked by extension`);

if (extensionElements.length > 0) {
  console.log('✅ Extension appears to be active');
} else {
  console.log('❌ Extension does not appear to be active');
}

// 5. Check for any existing activate buttons
const existingActivateButtons = document.querySelectorAll('.ubuy-activate-container');
console.log(`\nFound ${existingActivateButtons.length} existing activate buttons`);

if (existingActivateButtons.length > 0) {
  console.log('✅ Activate buttons exist on the page');
  existingActivateButtons.forEach((btn, i) => {
    console.log(`  Button ${i + 1}:`, btn);
    console.log(`  Visible:`, btn.offsetWidth > 0 && btn.offsetHeight > 0);
  });
} else {
  console.log('❌ No activate buttons found on the page');
}

console.log('\n✅ Diagnosis complete!');
