{"version": "2.0.0", "tasks": [{"label": "npm: build", "type": "npm", "script": "build1", "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "npm: dev", "type": "npm", "script": "dev", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "isBackground": true, "problemMatcher": {"owner": "webpack", "pattern": {"regexp": "^(.*)$", "file": 1}, "background": {"activeOnStart": true, "beginsPattern": "webpack is watching the files", "endsPattern": "webpack compiled|Failed to compile"}}}]}