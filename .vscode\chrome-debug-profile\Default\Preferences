{"accessibility": {"captions": {"headless_caption_enabled": false, "live_caption_language": "en-US"}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "", "shortcuts_version": 1}, "autocomplete": {"retention_policy_last_version": 137}, "autofill": {"last_version_deduped": 137}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"app_window_placement": {"DevToolsApp": {"always_on_top": false, "bottom": 791, "left": 610, "maximized": false, "right": 1250, "top": 151, "work_area_bottom": 1600, "work_area_left": 198, "work_area_right": 2560, "work_area_top": 0}}, "has_seen_welcome_page": false, "window_placement": {"bottom": 1580, "left": -1533, "maximized": false, "right": -26, "top": 0, "work_area_bottom": 1600, "work_area_left": -2560, "work_area_right": 0, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 21825, "default_search_provider": {"guid": ""}, "devtools": {"last_open_timestamp": "**************", "preferences": {"closeable-tabs": "{\"security\":true,\"freestyler\":true,\"chrome-recorder\":true}", "console.sidebar-selected-filter": "\"message\"", "console.sidebar.width": "{\"vertical\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "currentDockState": "\"right\"", "elements.styles.sidebar.width": "{\"vertical\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "inspector-view.split-view-state": "{\"vertical\":{\"size\":0}}", "inspector.drawer-split-view-state": "{\"horizontal\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "inspectorVersion": "38", "panel-selected-tab": "\"console\"", "releaseNoteVersionSeen": "78", "resources-stylesheet-expanded": "true", "sources-panel-navigator-split-view-state": "{\"vertical\":{\"size\":0,\"showMode\":\"Both\"}}", "sources-panel-split-view-state": "{\"vertical\":{\"size\":0,\"showMode\":\"Both\"},\"horizontal\":{\"size\":0,\"showMode\":\"Both\"}}", "styles-pane-sidebar-tab-order": "{\"styles\":10,\"computed\":20}", "undefined-tab-order": "{\"sources.scope-chain\":10,\"sources.watch\":20}"}, "synced_preferences_sync_disabled": {"adorner-settings": "[{\"adorner\":\"grid\",\"isEnabled\":true},{\"adorner\":\"subgrid\",\"isEnabled\":true},{\"adorner\":\"flex\",\"isEnabled\":true},{\"adorner\":\"ad\",\"isEnabled\":true},{\"adorner\":\"scroll-snap\",\"isEnabled\":true},{\"adorner\":\"container\",\"isEnabled\":true},{\"adorner\":\"slot\",\"isEnabled\":true},{\"adorner\":\"top-layer\",\"isEnabled\":true},{\"adorner\":\"reveal\",\"isEnabled\":true},{\"adorner\":\"media\",\"isEnabled\":false},{\"adorner\":\"scroll\",\"isEnabled\":true}]", "syncedInspectorVersion": "38"}}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "42c713fb-6415-4fdc-8985-37b06c2ad2cc", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "137.0.7151.120", "pinned_extensions": ["idijcfipdeagmjjbmdeeca<PERSON><PERSON><PERSON><PERSON>"], "settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Discover great apps, games, extensions and themes for Google Chrome.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files\\Google\\Chrome\\Application\\137.0.7151.120\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "idijcfipdeagmjjbmdeecalleeefmojn": {"account_extension_type": 0, "active_permissions": {"api": ["activeTab", "alarms", "storage", "unlimitedStorage"], "explicit_host": ["https://trhbupjhzfojzyhvrqey.supabase.co/*", "https://userinfo.ubuyfirst.net/*"], "manifest_permissions": [], "scriptable_host": ["https://www.befr.ebay.be/*", "https://www.benl.ebay.be/*", "https://www.ebay.at/*", "https://www.ebay.ca/*", "https://www.ebay.ch/*", "https://www.ebay.co.uk/*", "https://www.ebay.com.au/*", "https://www.ebay.com.hk/*", "https://www.ebay.com.my/*", "https://www.ebay.com.sg/*", "https://www.ebay.com/*", "https://www.ebay.de/*", "https://www.ebay.es/*", "https://www.ebay.fr/*", "https://www.ebay.ie/*", "https://www.ebay.in/*", "https://www.ebay.it/*", "https://www.ebay.nl/*", "https://www.ebay.ph/*", "https://www.ebay.pl/*"]}, "commands": {}, "content_settings": [], "creation_flags": 38, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "granted_permissions": {"api": ["activeTab", "alarms", "storage", "unlimitedStorage", "scripting"], "explicit_host": ["https://trhbupjhzfojzyhvrqey.supabase.co/*", "https://userinfo.ubuyfirst.net/*"], "manifest_permissions": [], "scriptable_host": ["https://www.befr.ebay.be/*", "https://www.benl.ebay.be/*", "https://www.ebay.at/*", "https://www.ebay.ca/*", "https://www.ebay.ch/*", "https://www.ebay.co.uk/*", "https://www.ebay.com.au/*", "https://www.ebay.com.hk/*", "https://www.ebay.com.my/*", "https://www.ebay.com.sg/*", "https://www.ebay.com/*", "https://www.ebay.de/*", "https://www.ebay.es/*", "https://www.ebay.fr/*", "https://www.ebay.ie/*", "https://www.ebay.in/*", "https://www.ebay.it/*", "https://www.ebay.nl/*", "https://www.ebay.ph/*", "https://www.ebay.pl/*"]}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 8, "newAllowFileAccess": true, "path": "C:\\ZOCS\\Visual Studio 2010\\Projects\\_EbayPrograms\\uBuyAssist\\dist\\chrome", "preferences": {}, "regular_only_preferences": {}, "service_worker_registration_info": {"version": "0.0.13"}, "serviceworkerevents": [], "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\137.0.7151.120\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "neajdppkdcdipfabeoofebfddakdcjhd": {"account_extension_type": 0, "active_permissions": {"api": ["metricsPrivate", "systemPrivate", "ttsEngine", "offscreen"], "explicit_host": ["https://www.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"service_worker": "service_worker.js"}, "description": "Component extension providing speech via the Google network text-to-speech service.", "host_permissions": ["https://www.google.com/"], "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA8GSbNUMGygqQTNDMFGIjZNcwXsHLzkNkHjWbuY37PbNdSDZ4VqlVjzbWqODSe+MjELdv5Keb51IdytnoGYXBMyqKmWpUrg+RnKvQ5ibWr4MW9pyIceOIdp9GrzC1WZGgTmZismYR3AjaIpufZ7xDdQQv+XrghPWCkdVqLN+qZDA1HU+DURznkMICiDDSH2sU0egm9UbWfS218bZqzKeQDiC3OnTPlaxcbJtKUuupIm5knjze3Wo9Ae9poTDMzKgchg0VlFCv3uqox+wlD8sjXBoyBCCK9HpImdVAF1a7jpdgiUHpPeV/26oYzM9/grltwNR3bzECQgSpyXp0eyoegwIDAQAB", "manifest_version": 3, "name": "Google Network Speech", "permissions": ["metricsPrivate", "offscreen", "systemPrivate", "ttsEngine"], "tts_engine": {"voices": [{"event_types": ["start", "end", "error"], "gender": "female", "lang": "de-DE", "remote": true, "voice_name": "Google Deutsch"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-US", "remote": true, "voice_name": "Google US English"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Female"}, {"event_types": ["start", "end", "error"], "gender": "male", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Male"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-ES", "remote": true, "voice_name": "Google español"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-US", "remote": true, "voice_name": "Google español de Estados Unidos"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "fr-FR", "remote": true, "voice_name": "Google français"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "hi-IN", "remote": true, "voice_name": "Google हिन्दी"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "id-ID", "remote": true, "voice_name": "Google Bahasa Indonesia"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "it-IT", "remote": true, "voice_name": "Google italiano"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ja-<PERSON>", "remote": true, "voice_name": "Google 日本語"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ko-KR", "remote": true, "voice_name": "Google 한국의"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "nl-NL", "remote": true, "voice_name": "Google Nederlands"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pl-PL", "remote": true, "voice_name": "Google polski"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pt-BR", "remote": true, "voice_name": "Google português do Brasil"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ru-RU", "remote": true, "voice_name": "Google русский"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-CN", "remote": true, "voice_name": "Google 普通话（中国大陆）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-HK", "remote": true, "voice_name": "Google 粤語（香港）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-TW", "remote": true, "voice_name": "Google 國語（臺灣）"}]}, "version": "1.0"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\137.0.7151.120\\resources\\network_speech_synthesis/mv3", "preferences": {}, "regular_only_preferences": {}, "service_worker_registration_info": {"version": "1.0"}, "serviceworkerevents": ["ttsEngine.onPause", "ttsEngine.onResume", "ttsEngine.onSpeak", "ttsEngine.onStop"], "was_installed_by_default": false, "was_installed_by_oem": false}, "nkeimhogjdpnpccoofpliimaahmaaome": {"account_extension_type": 0, "active_permissions": {"api": ["processes", "webrtcLoggingPrivate", "system.cpu", "enterprise.hardwarePlatform"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["runtime.onConnectExternal"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"page": "background.html", "persistent": false}, "externally_connectable": {"ids": ["moklfjoegmpoolceggbebbmgbddlhdgp", "ldmpofkllgeicjiihkimgeccbhghhmfj", "denipklgekfpcdmbahmbpnmokgajnhma", "kjfhgcncjdebkoofmbjoiemiboifnpbo", "ikfcpmgefdpheiiomgmhlmmkihchmdlj", "jlgegmdnodfhciolbdjciihnlaljdbjo", "lkbhffjfgpmpeppncnimiiikojibkhnm", "acdafoiapclbpdkhnighhilgampkglpc", "hkamnlhnogggfddmjomgbdokdkgfelgg"], "matches": ["https://*.meet.google.com/*"]}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB", "manifest_version": 2, "name": "Google Hangouts", "permissions": ["enterprise.hardwarePlatform", "processes", "system.cpu", "webrtcLoggingPrivate"], "version": "1.3.24"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\137.0.7151.120\\resources\\hangout_services", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "gaia_cookie": {"changed_time": **********.220011, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "com.chrome.windows"}, "google": {"services": {"signin_scoped_device_id": "c32f879b-3b6f-4cae-abe8-778d9825d9f1"}}, "history_clusters": {"all_cache": {"all_keywords": {}, "all_timestamp": "*****************"}}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "LensOverlay": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "*****************", "recent_session_start_times": ["*****************"], "session_last_active_time": "*****************", "session_start_time": "*****************"}, "intl": {"selected_languages": "en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"1013309121859": {}}}, "language_model_counters": {"en": 1}, "media": {"engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "ama+yf43rS77TRhStgU06Ll0XsD0S9g44eH9GFF2Ms8kb8WFsBILU15mraPpLjiXnk8ZH/LKH0DjBw8mWPZd6w=="}, "ntp": {"num_personal_suggestions": 3}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "predictionmodelfetcher": {"last_fetch_attempt": "*****************", "last_fetch_success": "*****************"}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PRICE_TRACKING": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true}, "pinned_tabs": [], "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true, "fledge_join_blocked": {}}, "profile": {"avatar_index": 26, "background_password_check": {"check_fri_weight": 9, "check_interval": "*************", "check_mon_weight": 8, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "*****************"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {"https://www.ebay.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [11, 13, 14]}}}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"https://[*.]ebay.com,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"https://www.ebay.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 14}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"chrome://extensions/,*": {"last_modified": "13395590338491008", "setting": {"lastEngagementTime": 1.3395590338491e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 6.0, "rawScore": 6.0}}, "https://www.ebay.com:443,*": {"last_modified": "13395592015276985", "setting": {"lastEngagementTime": 1.3395592015276976e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 15.0}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "137.0.7151.120", "creation_time": "13395589878852955", "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "exit_type": "Crashed", "family_member_role": "not_in_family", "last_engagement_time": "13395592015276976", "last_time_obsolete_http_credentials_removed": **********.923125, "last_time_password_store_metrics_reported": **********.918529, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "Your Chrome", "password_hash_data_list": [], "were_old_google_logins_removed": true}, "protection": {"macs": {"browser": {"show_home_button": "1A5C98EABEFA522FE682BDC6F9C07376751505706C61D87DB93CC9ED89E88DB2"}, "default_search_provider_data": {"template_url_data": "43C54F43902A0306A5369872FB5A55612C0E0936817F46DB08F191F90D8E2A76"}, "enterprise_signin": {"policy_recovery_token": "8D7AE0E6B9FE6BC1A5E5D3C9D0FF48E4D201AB6F7D666C23A2B3EFD28FD97347"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "C957A7E97D34834E5F9C0450478874532B5D8511CE4797B3C61307FA465AAC45", "idijcfipdeagmjjbmdeecalleeefmojn": "D46C5585AB981474EA79E6901DD0DB5E9C6E923F9C79C4F99E1A4DD7B6E8E2FE", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "6176C18C4B84EE42469F370A5E305A30044B727F692BFAD18150583AD0799A26", "neajdppkdcdipfabeoofebfddakdcjhd": "76CCDFE329BE2728B7C9EAFD07747297E2C5FB69210DFE7836D4AD737BBFC1AF", "nkeimhogjdpnpccoofpliimaahmaaome": "B28A32EBAE54A8123D510FB5BE6FEF6CF760A738965C050FEEECC4E98927629B"}, "ui": {"developer_mode": "E1F1F38CF43852DB29DFE2BA3C751689AF0C7F777411D9B9F88332E7AE6BB207"}}, "google": {"services": {"account_id": "E050669FD13DE643541B91152C55ED2298D25A1DF599215E35EB88F9173A2AEF", "last_signed_in_username": "2A3DF7B7633537376480958D9A4F086F8CC4A6AA19BD8418F9C0A257AE4A3F19", "last_username": "20D05FFD0674ABA71C3789E7033BEF57B9F0FBCD018CE4251DE67D154992E5C8"}}, "homepage": "FA7BB8CFE51CE7783CE3605B35894C8598AB8970C0B9DAAC468BAD4522CC0D5A", "homepage_is_newtabpage": "BD325E9F65C7099C29A11B05EC73F1F006925F6232234A0C6A0D145F9338A1D1", "media": {"cdm": {"origin_data": "3895C1431EA81BA4F687B16DB31B57CA3FD946883566A92721710E67D9F195C6"}, "storage_id_salt": "BB1EE5A0AD5F306C2F1BBFA008BAC4C53AFFD3E5F85B126D19D2215F1F0C7F2F"}, "module_blocklist_cache_md5_digest": "8A5B6F7988F88832651FEE317C3DC92C02D1371F4E235C97D6FD4C5A3A526DFB", "pinned_tabs": "1B64FE2632AB7ADCD3A8A0614D7C1451DCA282CC3111E1E8E8CD4105FDA5ABD8", "prefs": {"preference_reset_time": "89D315B7886D3D7E05965C34BC591B58DC5A4D4F59B349A51A05A71C54BCB707"}, "safebrowsing": {"incidents_sent": "80311D54F87CEAA810F2248DF0375688425C4E677A69B6540A85C027DD353847"}, "search_provider_overrides": "8C745FD2BE26AFC842C368D5874B369A5CE7AC56532AA47881A8FED4BC19E1FB", "session": {"restore_on_startup": "F76886416B36B1685965EBE2685C85CCC18CA6C46303D69CD0197D1563745413", "startup_urls": "FFF6DD893CDFA1BB780E4ABF3F0A4943FDB3852809AAB05858010FC7F6180A9C"}}}, "safebrowsing": {"event_timestamps": {}, "hash_real_time_ohttp_expiration_time": "13395849079177760", "hash_real_time_ohttp_key": "2AAgETtEl5cjzDpksr9J2MiGvUsUaS87sxbZ6vEn9Mxqv18ABAABAAI=", "metrics_last_log_time": "13395589878", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "ClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQ8MHjhP3m5RcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADEL3C44T95uUX", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13395455999000000", "uma_in_sql_start_time": "13395589878865347"}, "sessions": {"event_log": [{"crashed": false, "time": "13395590613198786", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 0, "time": "13395590645708694", "type": 2, "window_count": 1}, {"crashed": false, "time": "13395590647463978", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 0, "time": "13395590656105167", "type": 2, "window_count": 1}, {"crashed": false, "time": "13395590665594921", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13395590669990673", "type": 2, "window_count": 1}, {"crashed": false, "time": "13395591163734085", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 0, "time": "13395591203990772", "type": 2, "window_count": 1}, {"crashed": false, "time": "13395591211810521", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 0, "time": "13395591424882250", "type": 2, "window_count": 1}, {"crashed": false, "time": "13395591431213049", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 0, "time": "13395591555247585", "type": 2, "window_count": 1}, {"crashed": false, "time": "13395591561459045", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 0, "time": "13395591715443568", "type": 2, "window_count": 1}, {"crashed": false, "time": "13395591722276311", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 0, "time": "13395591735354936", "type": 2, "window_count": 1}, {"crashed": false, "time": "*****************", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 0, "time": "*****************", "type": 2, "window_count": 1}, {"crashed": false, "time": "*****************", "type": 0}], "session_data_status": 1}, "settings": {"force_google_safesearch": false}, "signin": {"allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["en-US"]}, "sync": {"passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "137"}}