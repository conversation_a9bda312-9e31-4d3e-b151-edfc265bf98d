{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Launch Program", "skipFiles": ["<node_internals>/**"], "runtimeExecutable": "npm", "sourceMaps": true, "runtimeArgs": ["run", "dev"]}, {"name": "Debug Extension Popup", "type": "chrome", "request": "launch", "url": "chrome://extensions/", "webRoot": "${workspaceFolder}", "sourceMaps": true, "preLaunchTask": "npm: dev", "outFiles": ["${workspaceFolder}/dist/**/*.js"], "runtimeExecutable": "C:/Program Files/Google/Chrome/Application/chrome.exe", "runtimeArgs": ["--remote-debugging-port=9222", "--load-extension=${workspaceFolder}/dist/chrome", "--extensions-on-chrome-urls"], "userDataDir": "${workspaceFolder}/.vscode/chrome-debug-profile", "sourceMapPathOverrides": {"webpack:///./*": "${webRoot}/*"}}, {"name": "Debug Content ", "type": "chrome", "request": "launch", "url": "https://www.ebay.com/sch/i.html?_nkw=test", "webRoot": "${workspaceFolder}", "sourceMaps": true, "preLaunchTask": "npm: dev", "runtimeExecutable": "C:/Program Files/Google/Chrome/Application/chrome.exe", "runtimeArgs": ["--remote-debugging-port=9222", "--load-extension=${workspaceFolder}/dist/chrome"], "userDataDir": "${workspaceFolder}/.vscode/chrome-debug-profile"}, {"name": "Debug Content Script", "type": "chrome", "request": "launch", "url": "https://www.ebay.com/sch/i.html?_nkw=test", "webRoot": "${workspaceFolder}", "sourceMaps": true, "preLaunchTask": "npm: dev", "runtimeExecutable": "C:/Program Files/Google/Chrome/Application/chrome.exe", "runtimeArgs": ["--remote-debugging-port=9222", "--load-extension=${workspaceFolder}/dist/chrome"], "userDataDir": "${workspaceFolder}/.vscode/chrome-debug-profile", "sourceMapPathOverrides": {"webpack:///./~/*": "${webRoot}/node_modules/*", "webpack:///./*": "${webRoot}/*", "webpack:///*": "*", "webpack:///src/*": "${webRoot}/*", "chrome-extension://*/content_scripts/*": "${webRoot}/content/*"}}, {"name": "Debug <PERSON>", "type": "chrome", "request": "launch", "url": "chrome://extensions/", "webRoot": "${workspaceFolder}", "sourceMaps": true, "preLaunchTask": "npm: dev", "outFiles": ["${workspaceFolder}/dist/**/*.js"], "runtimeExecutable": "C:/Program Files/Google/Chrome/Application/chrome.exe", "runtimeArgs": ["--remote-debugging-port=9222", "--load-extension=${workspaceFolder}/dist/chrome", "--extensions-on-chrome-urls"], "userDataDir": "${workspaceFolder}/.vscode/chrome-debug-profile", "sourceMapPathOverrides": {"webpack:///./*": "${webRoot}/*"}}]}