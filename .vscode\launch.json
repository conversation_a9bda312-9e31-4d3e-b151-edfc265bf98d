{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Launch Program", "skipFiles": ["<node_internals>/**"], "runtimeExecutable": "npm", "sourceMaps": true, "runtimeArgs": ["run", "dev"]}, {"name": "Debug Extension Popup", "type": "chrome", "request": "launch", "url": "chrome://extensions/", "webRoot": "${workspaceFolder}", "sourceMaps": true, "preLaunchTask": "npm: build", "outFiles": ["${workspaceFolder}/dist/**/*.js"], "runtimeExecutable": "C:/Program Files/Google/Chrome/Application/chrome.exe", "runtimeArgs": ["--remote-debugging-port=9222", "--load-extension=C:\\ZOCS\\Visual Studio 2010\\Projects\\_EbayPrograms\\uBuyAssist\\dist\\chrome", "--disable-extensions-except=C:\\ZOCS\\Visual Studio 2010\\Projects\\_EbayPrograms\\uBuyAssist\\dist\\chrome", "--extensions-on-chrome-urls"], "userDataDir": "${workspaceFolder}/.vscode/chrome-debug-profile", "sourceMapPathOverrides": {"webpack:///./*": "${webRoot}/*"}}, {"name": "Debug Content ", "type": "chrome", "request": "launch", "url": "https://www.ebay.com/sch/i.html?_nkw=test", "webRoot": "${workspaceFolder}", "sourceMaps": true, "preLaunchTask": "npm: build", "runtimeExecutable": "C:/Program Files/Google/Chrome/Application/chrome.exe", "runtimeArgs": ["--remote-debugging-port=9222", "--load-extension=C:\\ZOCS\\Visual Studio 2010\\Projects\\_EbayPrograms\\uBuyAssist\\dist\\chrome", "--disable-extensions-except=C:\\ZOCS\\Visual Studio 2010\\Projects\\_EbayPrograms\\uBuyAssist\\dist\\chrome"], "userDataDir": "${workspaceFolder}/.vscode/chrome-debug-profile"}, {"name": "Debug Content Script", "type": "chrome", "request": "launch", "url": "https://www.ebay.com/sch/i.html?_nkw=allen+bradley&_sacat=0&_from=R40", "webRoot": "${workspaceFolder}", "sourceMaps": true, "preLaunchTask": "npm: build", "runtimeExecutable": "C:/Program Files/Google/Chrome/Application/chrome.exe", "runtimeArgs": ["--remote-debugging-port=9222", "--load-extension=C:\\ZOCS\\Visual Studio 2010\\Projects\\_EbayPrograms\\uBuyAssist\\dist\\chrome", "--disable-extensions-except=C:\\ZOCS\\Visual Studio 2010\\Projects\\_EbayPrograms\\uBuyAssist\\dist\\chrome"], "userDataDir": "${workspaceFolder}/.vscode/chrome-debug-profile", "sourceMapPathOverrides": {"webpack:///./~/*": "${webRoot}/node_modules/*", "webpack:///./*": "${webRoot}/*", "webpack:///*": "*", "webpack:///src/*": "${webRoot}/*", "chrome-extension://*/content_scripts/*": "${webRoot}/content/*"}}, {"name": "Debug <PERSON>", "type": "chrome", "request": "launch", "url": "chrome://extensions/", "webRoot": "${workspaceFolder}", "sourceMaps": true, "preLaunchTask": "npm: build", "outFiles": ["${workspaceFolder}/dist/**/*.js"], "runtimeExecutable": "C:/Program Files/Google/Chrome/Application/chrome.exe", "runtimeArgs": ["--remote-debugging-port=9222", "--load-extension=C:\\ZOCS\\Visual Studio 2010\\Projects\\_EbayPrograms\\uBuyAssist\\dist\\chrome", "--disable-extensions-except=C:\\ZOCS\\Visual Studio 2010\\Projects\\_EbayPrograms\\uBuyAssist\\dist\\chrome", "--extensions-on-chrome-urls"], "userDataDir": "${workspaceFolder}/.vscode/chrome-debug-profile", "sourceMapPathOverrides": {"webpack:///./*": "${webRoot}/*"}}]}