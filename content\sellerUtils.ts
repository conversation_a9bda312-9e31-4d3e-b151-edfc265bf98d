// sellerUtils.ts
import type { Settings, CountryInfo } from "./files/types";
import { countryData } from "./files/countryData";
import type { ShortUser } from "./files/User";
import { console_log, wait } from "../helper/helpers";

/**
 * Parses the seller information string into a structured object
 */
export function parseSellerInfo(registrationDateStr: string): string {
  const regDate = new Date(
    `${registrationDateStr.slice(0, 4)}-${registrationDateStr.slice(4, 6)}-${registrationDateStr.slice(6, 8)}`,
  );

  const today = new Date();
  const timeDiff = today.getTime() - regDate.getTime();
  const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
  const monthsDiff = Math.floor(daysDiff / 30);
  const yearsDiff = Math.floor(monthsDiff / 12);

  const sellerAge = yearsDiff > 0 ? `${yearsDiff}y` : monthsDiff > 0 ? `${monthsDiff}m` : `${daysDiff}d`;

  return sellerAge;
}

export function get_seller_name(root_element: HTMLElement): { seller_name: string; seller_element: HTMLElement; } {
  const sellerElementsMap = new Map<string, HTMLElement[]>();

  // --- Old Layout Selectors ---

  // extract seller element from item pages
  let element = root_element.querySelector(`div[class*='about-seller'] a`) as HTMLElement;
  if (element) {
    let seller_name = null;
    console_log("seller_name", seller_name);
    try {
      // @ts-ignore
      seller_name = element.href.match(/\/str\/(.+?)(\?|$)/)[1];
    } catch (e) { }
    if (seller_name) {
      const sellerName = seller_name;
      // @ts-ignore
      return { seller_name: sellerName, seller_element: element.firstElementChild || element };
    } else {
      const sellerInfoText = element.textContent?.trim();
      if (sellerInfoText) {
        const sellerName = sellerInfoText;
        return { seller_name: sellerName, seller_element: element };
      }
    }

    // return { seller_element: element, seller_name: }
  }

  // Extract seller elements from .s-item__seller-info-text
  const sellerInfoElements = root_element.querySelectorAll<HTMLSpanElement>(".s-item__seller-info-text");
  for (let element of sellerInfoElements) {
    const sellerInfoText = element.textContent || "";
    const sellerNameMatch = sellerInfoText.match(/^(.+?) \(\d+[,\.\d]*\)/);
    if (sellerNameMatch) {
      const sellerName = sellerNameMatch[1].trim();
      return { seller_name: sellerName, seller_element: element };
    }
  }

  // Extract seller elements from .s-item__etrs-text .PRIMARY
  const sellerEtrsElements = root_element.querySelectorAll<HTMLSpanElement>(".s-item__etrs-text .PRIMARY");
  for (let element of sellerEtrsElements) {
    const sellerName = element.textContent?.trim() || "";
    if (sellerName && !/%/.test(sellerName) && sellerName.length > 1) {
      return { seller_name: sellerName, seller_element: element };
    }
  }
  // --- New Layout Selector ---
  // Find the secondary attributes containers first
  const secondaryAttributeContainers = root_element.querySelectorAll(".su-card-container__attributes__secondary");
  for (let container of secondaryAttributeContainers) {
    // Find all attribute rows within this container
    const attributeRows = container.querySelectorAll<HTMLDivElement>(".s-card__attribute-row");

    for (let row of attributeRows) {
      // Find the specific span we're interested in within this row
      const element = row.querySelector<HTMLDivElement>(".su-styled-text.secondary.large");

      if (element) {
        const sellerInfoText = element.textContent || "";
        // Example: "sellername (58) 96.8%" or "hoffgate (319) 100%"
        // Use the specific regex to confirm this is the seller info span
        const sellerNameMatch = sellerInfoText.match(/^(.+?) \(\d+[,\.\d]*\) \d+(\.\d+)?%/);
        if (sellerNameMatch) {
          const sellerName = sellerNameMatch[1].trim();
          return { seller_name: sellerName, seller_element: element };
        }
      }
    }
  }

  // --- Enhanced New Layout Selector (Fallback) ---
  // Sometimes the structure might be slightly different, so let's try a broader search
  const allAttributeRows = root_element.querySelectorAll<HTMLDivElement>(".s-card__attribute-row");
  for (let row of allAttributeRows) {
    // Look for any span with seller-like text pattern, regardless of exact classes
    const spans = row.querySelectorAll<HTMLSpanElement>("span");
    for (let span of spans) {
      const sellerInfoText = span.textContent || "";
      // Check if this looks like seller info: "sellername (count) percentage%"
      const sellerNameMatch = sellerInfoText.match(/^(.+?) \(\d+[,\.\d]*\) \d+(\.\d+)?%$/);
      if (sellerNameMatch) {
        const sellerName = sellerNameMatch[1].trim();
        // Additional validation: make sure this isn't a price or other info
        if (!sellerName.includes('$') && !sellerName.includes('€') && !sellerName.includes('£')) {
          return { seller_name: sellerName, seller_element: span };
        }
      }
    }
  }

  return { seller_name: "_not_found_", seller_element: root_element };
}

export function get_product_id(root_element: HTMLElement): string {
  let item = root_element;
  const sItemLink = item.querySelector<HTMLAnchorElement>("a.s-item__link, a.su-link");
  if (sItemLink) {
    const baseUrl = sItemLink.href.split("?")[0];
    const itemIdMatch = baseUrl.match(/\/itm\/(\d+)/);
    const itemId = itemIdMatch ? itemIdMatch[1] : "_not_found_";
    return itemId;
  }
  return "_not_found_";
}

export function processSellerElement(
  search_result_element: HTMLElement,
  sellerElement: HTMLElement,
  sellerName: string,
  shortUser: ShortUser,
  settings: Settings,
): void {
  const countryId = shortUser?.sc ?? shortUser?.uc ?? undefined;
  if (!countryId) return;

  const country = countryData[countryId];
  if (!country) return;

  //
  // Find the parent item container for either old or new layout
  const item = search_result_element;
  console_log("sellerElement", sellerElement, item, sellerName);
  // Add seller information if not hidden
  let insertionPoint: Element | null | undefined = null;
  let containerForExistingCheck: Element | null | undefined = null;

  // Determine insertion point based on layout
  if (item.matches("li.s-item")) {
    // Old layout: Insert after the parent of the seller info span's parent
    insertionPoint = sellerElement.parentElement?.parentElement;
    containerForExistingCheck = insertionPoint?.parentElement; // Check within the item container
  } else if (item.matches("li.su-card-container")) {
    // New layout: Insert after the seller name span itself
    insertionPoint = sellerElement; // The element found by getSellerElements
    containerForExistingCheck = item; // Check within the item container
  } else if (search_result_element.matches(`[data-testid="x-sellercard-atf"]`)) {
    // this is an item page
    // @ts-ignore
    insertionPoint = sellerElement.parentElement.parentElement;
    containerForExistingCheck = item;
  }

  if (!insertionPoint || !containerForExistingCheck) return;

  // Remove existing seller info if it exists within the correct container
  const existingInfo = containerForExistingCheck.querySelector(".ubuy-seller-info");
  existingInfo?.remove();

  // Build and insert new seller content
  const sellerContent = `
          <span class='ubuy-seller-info' style="display: block; margin-top: 4px; margin-bottom: 4px;">
            ${buildSellerContent(shortUser, country, settings)}
          </span>`;
  // Use insertAdjacentHTML on the determined insertion point
  insertionPoint.insertAdjacentHTML("afterend", sellerContent);
  //
  //
  /**
   * Adds a menu to block sellers.
   */
  const blockButton = createBlockSellerButton(sellerName);
  document.body.appendChild(blockButton);
  addBlockButtonEventListeners(sellerElement, blockButton);
  //
  //
}

// Block seller button
function addBlockButtonEventListeners(seller: HTMLSpanElement, blockButton: HTMLButtonElement) {
  let blockButtonTimeout: NodeJS.Timeout;

  // Add cursor pointer and transition to seller element
  seller.style.cssText = `
    cursor: pointer;
    transition: all 0.2s ease;
  `;

  seller.addEventListener("mouseenter", () => {
    // Emphasize seller name
    //seller.style.color = "#2563eb";
    //seller.style.textDecoration = "underline";
    if (seller.dataset.ubuy_enabled !== "false") {
      seller.style.fontWeight = "600";
      blockButtonTimeout = setTimeout(() => {
        const rect = seller.getBoundingClientRect();
        blockButton.style.left = `${rect.left}px`;
        blockButton.style.top = `${rect.bottom + window.scrollY}px`;
        blockButton.style.display = "block";
      }, 300);
    }
  });

  seller.addEventListener("mouseleave", () => {
    // Reset seller name styles
    seller.style.color = "";
    seller.style.textDecoration = "";
    seller.style.fontWeight = "";

    clearTimeout(blockButtonTimeout);
    setTimeout(() => {
      if (!blockButton.matches(":hover")) {
        blockButton.style.display = "none";
      }
    }, 500);
  });

  blockButton.addEventListener("mouseleave", () => {
    blockButton.style.display = "none";
  });
  blockButton.addEventListener("click", async () => {
    await wait(100);
    blockButton.style.display = "none";
  });
}

function createBlockSellerButton(sellerName: string): HTMLButtonElement {
  const blockButton = document.createElement("button");
  blockButton.textContent = "Block Seller";
  blockButton.dataset.seller_name = sellerName;
  blockButton.classList.add("ubuy-block-seller-button");

  // Add hover effect
  blockButton.addEventListener("mouseover", () => {
    blockButton.style.backgroundColor = "#ff6b81";
    blockButton.style.boxShadow = "0 4px 8px rgba(0, 0, 0, 0.2)";
  });

  blockButton.addEventListener("mouseout", () => {
    blockButton.style.backgroundColor = "#ff4757";
    blockButton.style.boxShadow = "0 2px 4px rgba(0, 0, 0, 0.2)";
  });

  blockButton.addEventListener("click", (event) => {
    console_log("handleBlockButtonClick");
    // handleBlockButtonClick(event, seller, blockedSellers, setBlockedSellers),
  });

  return blockButton;
}

/**
 * Builds the HTML content to display seller information
 */
export function buildSellerContent(shortUser: ShortUser, country: CountryInfo, settings: Settings): string {
  const { rd, b, tp } = shortUser;
  const flagPath = chrome.runtime.getURL(`/images/flags/${country.iso}.SVG`);

  settings.showFlag;
  let flagImg = `<img data-s="showFlag" src="${flagPath}" alt="${country.iso}" title="${country.name}" style="width: 25px; height: auto;display: inline-block; vertical-align: middle;" />`;

  settings.showCountryIso;
  let countryIso = `<span data-s="showCountryIso" title="${country.name}">${country.iso}</span>`;

  settings.showStoreIndividual;
  let businessIcon = b
    ? '<span data-s="showStoreIndividual" title="Business Seller">💼</span>'
    : '<span data-s="showStoreIndividual" title="Individual Seller">👤</span>';

  settings.showAge;
  let ageInfo = `<span data-s="showAge" title="Seller Age">(${parseSellerInfo(rd ?? "")})</span>`;

  settings.showTransactionPercentage;
  let transactionPercentageInfo = ``;
  if (tp !== undefined) {
    transactionPercentageInfo = `<span data-s="showTransactionPercentage" title="Seller Sell/Buy ratio"><i><u>${tp}%</u></i></span>`;
  }

  let sellerContent = `${flagImg} ${countryIso} ${businessIcon} ${ageInfo} ${transactionPercentageInfo}`;
  return sellerContent;
}
