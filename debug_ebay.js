// Enhanced debug script to test uBuyAssist extension logic against current eBay layout
console.log("=== uBuyAssist Extension Debug Script ===");

// Test the extension's current selectors
function testExtensionSelectors() {
  console.log("\n=== TESTING EXTENSION SELECTORS ===");

  // Test main container selectors (from ControllerContent.ts line 46-47)
  const extensionSelectors = `li.s-item, li.su-card-container, [data-testid="x-sellercard-atf"]`;
  const elements = document.querySelectorAll(extensionSelectors);
  console.log(`Extension selector "${extensionSelectors}" found:`, elements.length, "elements");

  // Break down by individual selectors
  const oldItems = document.querySelectorAll("li.s-item");
  const newItems = document.querySelectorAll("li.su-card-container");
  const itemPages = document.querySelectorAll('[data-testid="x-sellercard-atf"]');

  console.log("- li.s-item (old layout):", oldItems.length);
  console.log("- li.su-card-container (new layout):", newItems.length);
  console.log('- [data-testid="x-sellercard-atf"] (item pages):', itemPages.length);

  return { elements, oldItems, newItems, itemPages };
}

// Test seller name extraction logic (from sellerUtils.ts)
function testSellerNameExtraction(rootElement) {
  console.log("\n--- Testing seller name extraction for element ---");
  console.log("Element classes:", rootElement.className);

  // Test new layout selector (from sellerUtils.ts lines 76-98)
  const secondaryAttributeContainers = rootElement.querySelectorAll(".su-card-container__attributes__secondary");
  console.log("Secondary attribute containers found:", secondaryAttributeContainers.length);

  for (let container of secondaryAttributeContainers) {
    const attributeRows = container.querySelectorAll(".s-card__attribute-row");
    console.log("  Attribute rows in container:", attributeRows.length);

    for (let row of attributeRows) {
      const element = row.querySelector(".su-styled-text.secondary.large");
      if (element) {
        const sellerInfoText = element.textContent || "";
        console.log("  Found text:", sellerInfoText);

        // Test the regex pattern
        const sellerNameMatch = sellerInfoText.match(/^(.+?) \(\d+[,\.\d]*\) \d+(\.\d+)?%/);
        if (sellerNameMatch) {
          const sellerName = sellerNameMatch[1].trim();
          console.log("  ✅ SELLER FOUND:", sellerName);
          console.log("  Element:", element);
          console.log("  Parent:", element.parentElement);
          return { seller_name: sellerName, seller_element: element };
        } else {
          console.log("  ❌ Regex did not match");
        }
      }
    }
  }

  // Test old layout selectors as fallback
  const sellerInfoElements = rootElement.querySelectorAll(".s-item__seller-info-text");
  if (sellerInfoElements.length > 0) {
    console.log("Old layout seller elements found:", sellerInfoElements.length);
    for (let element of sellerInfoElements) {
      const sellerInfoText = element.textContent || "";
      const sellerNameMatch = sellerInfoText.match(/^(.+?) \(\d+[,\.\d]*\)/);
      if (sellerNameMatch) {
        const sellerName = sellerNameMatch[1].trim();
        console.log("  ✅ OLD LAYOUT SELLER FOUND:", sellerName);
        return { seller_name: sellerName, seller_element: element };
      }
    }
  }

  console.log("  ❌ No seller found in this element");
  return { seller_name: "_not_found_", seller_element: rootElement };
}

// Test activate button injection logic
function testActivateButtonInjection(sellerElement, searchResultElement) {
  console.log("\n--- Testing activate button injection ---");

  // Check if button already exists (from ControllerContent.ts line 639)
  const existingButton = searchResultElement.querySelector(".ubuy-activate-container");
  if (existingButton) {
    console.log("  ✅ Activate button already exists");
    return existingButton;
  }

  if (sellerElement.parentElement) {
    console.log("  Seller element parent:", sellerElement.parentElement);
    console.log("  Parent classes:", sellerElement.parentElement.className);

    // Create test button (don't actually inject to avoid conflicts)
    const testButton = document.createElement("span");
    testButton.className = "ubuy-activate-container-test";
    testButton.innerHTML =
      '<button class="ubuy-activate-button-test" style="background: red; color: white; padding: 2px 8px; border: none; cursor: pointer;">TEST ACTIVATE</button>';

    console.log("  Would inject button after:", sellerElement.parentElement);
    console.log("  ✅ Button injection point identified");
    return testButton;
  } else {
    console.log("  ❌ No parent element found for injection");
    return null;
  }
}

// Main test function
function runExtensionTests() {
  const { elements, newItems } = testExtensionSelectors();

  console.log("\n=== TESTING SELLER DETECTION ON FIRST 3 NEW LAYOUT ITEMS ===");

  const testItems = Array.from(newItems).slice(0, 3);

  for (let i = 0; i < testItems.length; i++) {
    const item = testItems[i];
    console.log(`\n--- Testing item ${i + 1} ---`);
    console.log("Item ID:", item.id);
    console.log("Item classes:", item.className);

    const result = testSellerNameExtraction(item);

    if (result.seller_name !== "_not_found_") {
      console.log("✅ Seller detection successful");
      const buttonTest = testActivateButtonInjection(result.seller_element, item);
      if (buttonTest) {
        console.log("✅ Button injection would work");
      }
    } else {
      console.log("❌ Seller detection failed");
    }
  }

  // Test current page structure
  console.log("\n=== CURRENT PAGE STRUCTURE ANALYSIS ===");
  console.log("URL:", window.location.href);
  console.log("Total search result containers found:", elements.length);

  if (elements.length === 0) {
    console.log("❌ No search result containers found - extension may not activate");
  } else {
    console.log("✅ Search result containers found - extension should activate");
  }
}

// Run the tests
runExtensionTests();
