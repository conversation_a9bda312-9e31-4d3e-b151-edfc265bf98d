// Quick test - paste this into browser console on eBay search results page
console.log('🔍 Quick uBuyAssist Test');

// Check for new layout items
const items = document.querySelectorAll('li.su-card-container');
console.log(`Found ${items.length} new layout items`);

if (items.length > 0) {
  const firstItem = items[0];
  console.log('Testing first item:', firstItem.id);
  
  // Look for seller info
  const spans = firstItem.querySelectorAll('span');
  let sellerFound = false;
  
  for (const span of spans) {
    const text = span.textContent.trim();
    if (text.match(/^.+? \(\d+\) \d+%$/)) {
      console.log('✅ Found seller:', text);
      console.log('Element:', span);
      console.log('Classes:', span.className);
      sellerFound = true;
      
      // Test button injection
      const testBtn = document.createElement('div');
      testBtn.innerHTML = '<button style="background:red;color:white;padding:4px;">TEST</button>';
      span.parentElement.after(testBtn);
      console.log('✅ Test button added');
      
      setTimeout(() => testBtn.remove(), 3000);
      break;
    }
  }
  
  if (!sellerFound) {
    console.log('❌ No seller found');
  }
} else {
  console.log('❌ No new layout items found');
}
